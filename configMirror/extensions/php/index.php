<?php

/**
 * PHP 版本主索引文件
 * 
 * 此文件由版本拆分工具自动生成
 * 生成时间: 2025-05-31 00:15:49
 */

return [
    'structure_version' => '2.0',
    'split_date' => '2025-05-31 00:15:49',
    'major_versions' => [
        '5.4' => [
            'version_count' => 46,
            'first_version' => '5.4.0',
            'latest_version' => '5.4.45',
            'config_file' => '5.4/versions.php',
            'metadata_file' => '5.4/metadata.php',
        ],
        '5.5' => [
            'version_count' => 39,
            'first_version' => '5.5.0',
            'latest_version' => '5.5.38',
            'config_file' => '5.5/versions.php',
            'metadata_file' => '5.5/metadata.php',
        ],
        '5.6' => [
            'version_count' => 41,
            'first_version' => '5.6.0',
            'latest_version' => '5.6.40',
            'config_file' => '5.6/versions.php',
            'metadata_file' => '5.6/metadata.php',
        ],
        '7.0' => [
            'version_count' => 34,
            'first_version' => '7.0.0',
            'latest_version' => '7.0.33',
            'config_file' => '7.0/versions.php',
            'metadata_file' => '7.0/metadata.php',
        ],
        '7.1' => [
            'version_count' => 34,
            'first_version' => '7.1.0',
            'latest_version' => '7.1.33',
            'config_file' => '7.1/versions.php',
            'metadata_file' => '7.1/metadata.php',
        ],
        '7.2' => [
            'version_count' => 35,
            'first_version' => '7.2.0',
            'latest_version' => '7.2.34',
            'config_file' => '7.2/versions.php',
            'metadata_file' => '7.2/metadata.php',
        ],
        '7.3' => [
            'version_count' => 34,
            'first_version' => '7.3.0',
            'latest_version' => '7.3.33',
            'config_file' => '7.3/versions.php',
            'metadata_file' => '7.3/metadata.php',
        ],
        '7.4' => [
            'version_count' => 33,
            'first_version' => '7.4.0',
            'latest_version' => '7.4.33',
            'config_file' => '7.4/versions.php',
            'metadata_file' => '7.4/metadata.php',
        ],
        '8.0' => [
            'version_count' => 30,
            'first_version' => '8.0.0',
            'latest_version' => '8.0.30',
            'config_file' => '8.0/versions.php',
            'metadata_file' => '8.0/metadata.php',
        ],
        '8.1' => [
            'version_count' => 33,
            'first_version' => '8.1.0',
            'latest_version' => '8.1.32',
            'config_file' => '8.1/versions.php',
            'metadata_file' => '8.1/metadata.php',
        ],
        '8.2' => [
            'version_count' => 29,
            'first_version' => '8.2.0',
            'latest_version' => '8.2.28',
            'config_file' => '8.2/versions.php',
            'metadata_file' => '8.2/metadata.php',
        ],
        '8.3' => [
            'version_count' => 22,
            'first_version' => '8.3.0',
            'latest_version' => '8.3.21',
            'config_file' => '8.3/versions.php',
            'metadata_file' => '8.3/metadata.php',
        ],
        '8.4' => [
            'version_count' => 8,
            'first_version' => '8.4.0',
            'latest_version' => '8.4.7',
            'config_file' => '8.4/versions.php',
            'metadata_file' => '8.4/metadata.php',
        ],
    ],
    'summary' => [
        'total_major_versions' => 13,
        'total_versions' => 418,
        'version_range' => [
            'oldest' => '5.4.0',
            'newest' => '8.4.7',
        ],
    ],
    'global_metadata' => [
        'total_versions' => 418,
        'last_updated' => '2025-05-30 23:59:29',
        'discovery_source' => 'https://www.php.net/releases/index.php?json=1',
        'auto_updated' => true,
    ],
    'usage' => [
        'load_all' => 'require_once \"index.php\"; $allVersions = loadAllPhpVersions();',
        'load_major' => 'require_once \"8.3/versions.php\"; $php83 = $config[\"versions\"];',
        'get_metadata' => 'require_once \"8.3/metadata.php\"; $meta = $config;',
    ],
];
