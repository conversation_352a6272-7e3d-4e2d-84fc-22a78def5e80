<?php

/**
 * redis 版本配置文件
 * 
 * 此文件由版本发现服务自动更新
 * 最后更新时间: 2025-05-29 00:04:08
 */

return [
    'name' => 'redis',
    'type' => 'pecl',
    'description' => 'PHP extension for interfacing with Red<PERSON>',
    'version_range' => [
        '5.3.7',
        '6.0.2',
    ],
    'all_versions' => [
        '2.2.3',
        '2.2.4',
        '2.2.5',
        '2.2.7',
        '2.2.8',
        '3.0.0',
        '3.1.0',
        '3.1.1',
        '3.1.2',
        '3.1.3',
        '3.1.4',
        '3.1.5',
        '3.1.6',
        '4.0.0',
        '4.0.1',
        '4.0.2',
        '4.1.0',
        '4.1.1',
        '4.2.0',
        '4.3.0',
        '5.0.0',
        '5.0.1',
        '5.0.2',
        '5.1.0',
        '5.1.1',
        '5.2.0',
        '5.2.1',
        '5.2.2',
        '5.3.0',
        '5.3.1',
        '5.3.2',
        '5.3.3',
        '5.3.4',
        '5.3.5',
        '5.3.6',
        '5.3.7',
        '6.0.0',
        '6.0.1',
        '6.0.2',
        '6.1.0',
        '6.2.0',
    ],
    'recommended_versions' => [
        '2.2.5',
        '2.2.7',
        '2.2.8',
        '3.1.4',
        '3.1.5',
        '3.1.6',
        '4.1.1',
        '4.2.0',
        '4.3.0',
        '5.3.5',
        '5.3.6',
        '5.3.7',
        '6.0.2',
        '6.1.0',
        '6.2.0',
    ],
    'filter' => [
        'stable_only' => true,
        'exclude_patterns' => [
            '/alpha/',
            '/beta/',
            '/RC/',
        ],
    ],
    'metadata' => [
        'total_discovered' => 41,
        'total_recommended' => 15,
        'last_updated' => '2025-05-29 00:04:08',
        'discovery_source' => 'https://pecl.php.net/rest/r/redis/allreleases.xml',
        'auto_updated' => true,
    ],
];
