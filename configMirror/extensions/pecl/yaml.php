<?php

/**
 * yaml 版本配置文件
 * 
 * 此文件由版本发现服务自动更新
 * 最后更新时间: 2025-05-29 00:04:08
 */

return [
    'name' => 'yaml',
    'type' => 'pecl',
    'description' => 'YAML-1.1 parser and emitter',
    'version_range' => [
        '2.2.2',
        '2.2.3',
    ],
    'all_versions' => [
        '1.0.0',
        '1.0.1',
        '1.1.0',
        '1.1.1',
        '1.2.0',
        '1.3.0',
        '1.3.1',
        '1.3.2',
        '2.0.0',
        '2.0.2',
        '2.0.3',
        '2.0.4',
        '2.1.0',
        '2.2.0',
        '2.2.1',
        '2.2.2',
        '2.2.3',
        '2.2.4',
    ],
    'recommended_versions' => [
        '1.3.0',
        '1.3.1',
        '1.3.2',
        '2.2.2',
        '2.2.3',
        '2.2.4',
    ],
    'filter' => [
        'stable_only' => true,
        'exclude_patterns' => [
            '/alpha/',
            '/beta/',
            '/RC/',
        ],
    ],
    'metadata' => [
        'total_discovered' => 18,
        'total_recommended' => 6,
        'last_updated' => '2025-05-29 00:04:08',
        'discovery_source' => 'https://pecl.php.net/rest/r/yaml/allreleases.xml',
        'auto_updated' => true,
    ],
];
