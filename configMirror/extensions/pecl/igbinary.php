<?php

/**
 * igbinary 版本配置文件
 * 
 * 此文件由版本发现服务自动更新
 * 最后更新时间: 2025-05-29 00:04:08
 */

return [
    'name' => 'igbinary',
    'type' => 'pecl',
    'description' => 'Igbinary is a drop in replacement for the standard php serializer',
    'version_range' => [
        '3.2.7',
        '3.2.14',
    ],
    'all_versions' => [
        '1.1.1',
        '1.2.0',
        '1.2.1',
        '2.0.0',
        '2.0.1',
        '2.0.2',
        '2.0.3',
        '2.0.4',
        '2.0.5',
        '2.0.6',
        '2.0.7',
        '2.0.8',
        '3.0.0',
        '3.0.1',
        '3.1.0',
        '3.1.1',
        '3.1.2',
        '3.1.3',
        '3.1.4',
        '3.1.5',
        '3.1.6',
        '3.2.0',
        '3.2.1',
        '3.2.2',
        '3.2.3',
        '3.2.4',
        '3.2.5',
        '3.2.6',
        '3.2.7',
        '3.2.8',
        '3.2.9',
        '3.2.10',
        '3.2.11',
        '3.2.12',
        '3.2.13',
        '3.2.14',
        '3.2.15',
        '3.2.16',
    ],
    'recommended_versions' => [
        '1.1.1',
        '1.2.0',
        '1.2.1',
        '2.0.6',
        '2.0.7',
        '2.0.8',
        '3.2.14',
        '3.2.15',
        '3.2.16',
    ],
    'filter' => [
        'stable_only' => true,
        'exclude_patterns' => [
            '/alpha/',
            '/beta/',
            '/RC/',
        ],
    ],
    'metadata' => [
        'total_discovered' => 38,
        'total_recommended' => 9,
        'last_updated' => '2025-05-29 00:04:08',
        'discovery_source' => 'https://pecl.php.net/rest/r/igbinary/allreleases.xml',
        'auto_updated' => true,
    ],
];
