<?php

/**
 * redis 版本配置文件
 * 
 * 此文件由版本发现服务自动更新
 * 最后更新时间: 2025-05-29 00:04:15
 */

return [
    'name' => 'redis',
    'type' => 'github',
    'description' => 'PHP extension for interfacing with <PERSON><PERSON>',
    'repository' => 'phpredis/phpredis',
    'source' => 'https://github.com/phpredis/phpredis/archive/refs/tags',
    'pattern' => '{version}.tar.gz',
    'all_versions' => [
        '1.2.0',
        '2.0.0',
        '2.0.1',
        '2.0.2',
        '2.0.3',
        '2.0.4',
        '2.0.5',
        '2.0.6',
        '2.0.7',
        '2.0.8',
        '2.0.9',
        '2.0.10',
        '2.0.11',
        '2.0.12',
        '2.1.0',
        '2.1.1',
        '2.1.2',
        '2.1.3',
        '2.2.0',
        '2.2.1',
        '2.2.2',
        '2.2.3',
        '2.2.4',
        '2.2.5',
        '2.2.6',
        '2.2.7',
        '2.2.8',
        '3.0.0',
        '3.1.0',
        '3.1.1',
        '3.1.2',
        '3.1.3',
        '3.1.4',
        '3.1.5',
        '3.1.6',
        '4.0.0',
        '4.0.1',
        '4.0.2',
        '4.1.0',
        '4.1.1',
        '4.2.0',
        '4.3.0',
        '5.0.0',
        '5.0.1',
        '5.0.2',
        '5.1.0',
        '5.1.1',
        '5.2.0',
        '5.2.1',
        '5.2.2',
        '5.3.0',
        '5.3.1',
        '5.3.2',
        '5.3.3',
        '5.3.4',
        '5.3.5',
        '5.3.6',
        '5.3.7',
        '6.0.0',
        '6.0.1',
        '6.0.2',
        '6.1.0',
        '6.2.0',
    ],
    'recommended_versions' => [
        '1.2.0',
        '2.2.6',
        '2.2.7',
        '2.2.8',
        '3.1.4',
        '3.1.5',
        '3.1.6',
        '4.1.1',
        '4.2.0',
        '4.3.0',
        '5.3.5',
        '5.3.6',
        '5.3.7',
        '6.0.2',
        '6.1.0',
        '6.2.0',
    ],
    'filter' => [
        'stable_only' => false,
        'exclude_patterns' => [
            '/alpha/',
            '/beta/',
        ],
    ],
    'metadata' => [
        'total_discovered' => 63,
        'total_recommended' => 16,
        'last_updated' => '2025-05-29 00:04:15',
        'discovery_source' => 'https://api.github.com/repos/phpredis/phpredis/tags',
        'auto_updated' => true,
    ],
];
