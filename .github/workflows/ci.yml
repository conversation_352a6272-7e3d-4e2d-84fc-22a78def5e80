name: BATS CI

on:
  push:
    branches: [ master1 ]
  pull_request:
    branches: [ master1 ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        php: ['7.4', '8.0', '8.1', '8.2']
        
    name: PHP ${{ matrix.php }} BATS Test
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          tools: composer
          
      - name: Install dependencies
        run: composer install --prefer-dist --no-progress
          
      - name: Install BATS
        run: sudo apt-get install -y bats
          
      - name: Run BATS tests
        run: bats tests/bats/