<?php

/**
 * PHP版本已知问题配置
 * 
 * 返回格式：[PHP版本] = [问题1, 问题2, ...]
 */
return [
    '8.3' => [
        '在较旧的发行版上可能需要更新系统库',
        '在ARMv7架构上编译可能会失败',
    ],
    '8.2' => [
        '在某些较旧的发行版上可能需要手动安装依赖',
    ],
    '8.1' => [
        '在ARMv7架构的较旧系统上可能存在性能问题',
    ],
    '8.0' => [
        '在某些Alpine版本上可能需要额外的编译选项',
    ],
    '7.4' => [
        '在最新的发行版上可能需要特定的编译选项',
    ],
    '7.3' => [
        '在最新的发行版上可能存在依赖冲突',
    ],
    '7.2' => [
        '在最新的发行版上可能不受官方支持',
    ],
    '7.1' => [
        '已不再受官方支持，可能存在安全风险',
        '在某些新版本系统上可能无法正常编译',
    ],
];
