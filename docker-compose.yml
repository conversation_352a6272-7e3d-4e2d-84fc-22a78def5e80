version: '3.8'

services:
  # Ubuntu 容器
  ubuntu-18.04:
    image: pvm-ubuntu-18.04
    container_name: pvm-ubuntu-18.04
    build:
      context: ./docker/dev/ubuntu-18.04
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash
    environment:
      - DEBIAN_FRONTEND=noninteractive

  ubuntu-20.04:
    image: pvm-ubuntu-20.04
    container_name: pvm-ubuntu-20.04
    build:
      context: ./docker/dev/ubuntu-20.04
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash
    environment:
      - DEBIAN_FRONTEND=noninteractive

  ubuntu-22.04:
    image: pvm-ubuntu-22.04
    container_name: pvm-ubuntu-22.04
    build:
      context: ./docker/dev/ubuntu-22.04
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash
    environment:
      - DEBIAN_FRONTEND=noninteractive

  ubuntu-24.04:
    image: pvm-ubuntu-24.04
    container_name: pvm-ubuntu-24.04
    build:
      context: ./docker/dev/ubuntu-24.04
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash
    environment:
      - DEBIAN_FRONTEND=noninteractive

  # 保留原有的 ubuntu 服务，指向 22.04
  ubuntu:
    image: pvm-ubuntu
    container_name: pvm-ubuntu
    build:
      context: ./docker/dev/ubuntu
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash
    environment:
      - DEBIAN_FRONTEND=noninteractive

  # Debian 容器
  debian-11:
    image: pvm-debian-11
    container_name: pvm-debian-11
    build:
      context: ./docker/dev/debian-11
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash
    environment:
      - DEBIAN_FRONTEND=noninteractive

  debian-12:
    image: pvm-debian-12
    container_name: pvm-debian-12
    build:
      context: ./docker/dev/debian-12
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash
    environment:
      - DEBIAN_FRONTEND=noninteractive

  # 保留原有的 debian 服务，指向 11
  debian:
    image: pvm-debian
    container_name: pvm-debian
    build:
      context: ./docker/dev/debian
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash
    environment:
      - DEBIAN_FRONTEND=noninteractive

  # CentOS 容器
  centos-7:
    image: pvm-centos-7
    container_name: pvm-centos-7
    build:
      context: ./docker/dev/centos-7
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash

  centos-stream-8:
    image: pvm-centos-stream-8
    container_name: pvm-centos-stream-8
    build:
      context: ./docker/dev/centos-stream-8
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash

  centos-stream-9:
    image: pvm-centos-stream-9
    container_name: pvm-centos-stream-9
    build:
      context: ./docker/dev/centos-stream-9
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash

  # 保留原有的 centos 服务，指向 7
  centos:
    image: pvm-centos
    container_name: pvm-centos
    build:
      context: ./docker/dev/centos
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash

  # Fedora 容器
  fedora-38:
    image: pvm-fedora-38
    container_name: pvm-fedora-38
    build:
      context: ./docker/dev/fedora-38
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash

  fedora-40:
    image: pvm-fedora-40
    container_name: pvm-fedora-40
    build:
      context: ./docker/dev/fedora-40
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash

  # 保留原有的 fedora 服务，指向 38
  fedora:
    image: pvm-fedora
    container_name: pvm-fedora
    build:
      context: ./docker/dev/fedora-38
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash

  # Alpine 容器
  alpine-3.16:
    image: pvm-alpine-3.16
    container_name: pvm-alpine-3.16
    build:
      context: ./docker/dev/alpine-3.16
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash

  alpine-3.18:
    image: pvm-alpine-3.18
    container_name: pvm-alpine-3.18
    build:
      context: ./docker/dev/alpine-3.18
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash

  alpine-3.19:
    image: pvm-alpine-3.19
    container_name: pvm-alpine-3.19
    build:
      context: ./docker/dev/alpine-3.19
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash

  # 保留原有的 alpine 服务，指向 3.16
  alpine:
    image: pvm-alpine
    container_name: pvm-alpine
    build:
      context: ./docker/dev/alpine
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/sh

  # ARM 架构模拟 (使用 QEMU)
  arm64:
    image: pvm-arm64
    container_name: pvm-arm64
    build:
      context: ./docker/dev/arm64
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash

  # Alibaba Linux 3 容器
  alinux3:
    image: pvm-alinux3
    container_name: pvm-alinux3
    build:
      context: ./docker/dev/alinux3
      dockerfile: Dockerfile
    volumes:
      - .:/app
    tty: true
    command: /bin/bash
