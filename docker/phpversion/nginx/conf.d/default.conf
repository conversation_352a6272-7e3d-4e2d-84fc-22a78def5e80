server {
    listen 80;
    server_name localhost;
    root /var/www/html;
    index index.php index.html;

    # PHP 7.1
    location ~ ^/php71/(.*)\.php$ {
        fastcgi_pass php71:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root/$1.php;
        include fastcgi_params;
    }

    # PHP 7.2
    location ~ ^/php72/(.*)\.php$ {
        fastcgi_pass php72:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root/$1.php;
        include fastcgi_params;
    }

    # PHP 7.3
    location ~ ^/php73/(.*)\.php$ {
        fastcgi_pass php73:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root/$1.php;
        include fastcgi_params;
    }

    # PHP 7.4
    location ~ ^/php74/(.*)\.php$ {
        fastcgi_pass php74:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root/$1.php;
        include fastcgi_params;
    }

    # PHP 8.0
    location ~ ^/php80/(.*)\.php$ {
        fastcgi_pass php80:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root/$1.php;
        include fastcgi_params;
    }

    # PHP 8.1
    location ~ ^/php81/(.*)\.php$ {
        fastcgi_pass php81:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root/$1.php;
        include fastcgi_params;
    }

    # PHP 8.2
    location ~ ^/php82/(.*)\.php$ {
        fastcgi_pass php82:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root/$1.php;
        include fastcgi_params;
    }

    # PHP 8.3
    location ~ ^/php83/(.*)\.php$ {
        fastcgi_pass php83:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root/$1.php;
        include fastcgi_params;
    }

    # 默认使用PHP 8.3
    location ~ \.php$ {
        fastcgi_pass php83:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location / {
        try_files $uri $uri/ =404;
    }
}
