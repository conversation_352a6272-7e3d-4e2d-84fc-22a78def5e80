# PHP Version Manager 用户界面

PHP Version Manager (PVM) 提供了两种用户界面：命令行界面和 Web 管理界面。本文档介绍这两种界面的使用方法和功能。

## 命令行界面

命令行界面是 PVM 的主要交互方式，提供了丰富的命令和选项，支持彩色输出、进度条显示和交互式菜单等功能。

### 基本命令

PVM 提供以下基本命令：

```bash
pvm help                # 显示帮助信息
pvm version             # 显示版本信息
pvm list                # 列出已安装的 PHP 版本
pvm install <version>   # 安装指定版本的 PHP
pvm use <version>       # 永久切换到指定版本的 PHP
pvm switch <version>    # 临时切换到指定版本的 PHP（仅在当前会话中有效）
pvm remove <version>    # 删除指定版本的 PHP
pvm ext list            # 列出已安装的扩展
pvm ext install <name>  # 安装指定扩展
pvm ext remove <name>   # 删除指定扩展
pvm composer            # 管理 Composer
pvm config              # 管理配置
pvm web                 # 启动 Web 管理界面
```

### 增强的命令行界面

PVM 的命令行界面支持以下增强功能：

#### 彩色输出

PVM 使用彩色输出来提高可读性和用户体验。不同类型的信息使用不同的颜色：

- 普通信息：蓝色
- 成功信息：绿色
- 警告信息：黄色
- 错误信息：红色

#### 进度条显示

在执行长时间操作（如下载和安装）时，PVM 会显示进度条，提供以下信息：

- 当前进度百分比
- 已完成/总任务数
- 预计剩余时间
- 已用时间
- 内存使用情况

示例：

```
下载 PHP 8.2.10... [===================>                   ] 65% (45.2 MB / 70.1 MB) 剩余: 15秒
```

#### 交互式菜单

PVM 提供交互式菜单，使用户可以方便地选择选项：

```
请选择 PHP 版本:
 1) 7.4.33
 2) 8.0.30
 3) 8.1.27
 4) 8.2.17
 5) 8.3.5
>
```

#### 多选菜单

对于需要选择多个选项的场景，PVM 提供多选菜单：

```
请选择要安装的扩展 (用逗号分隔多个选项):
 1) [✓] mysqli
 2) [✓] pdo
 3) [ ] gd
 4) [ ] curl
 5) [ ] zip
 a) [全选]
 n) [全不选]
 i) [反选]
>
```

#### 表格显示

PVM 使用表格格式显示结构化数据，提高可读性：

```
+--------+---------+----------+
| 版本   | 状态    | 安装日期 |
+--------+---------+----------+
| 7.4.33 | 已安装  | 2023-01-15 |
| 8.0.30 | 当前    | 2023-02-20 |
| 8.1.27 | 已安装  | 2023-03-10 |
+--------+---------+----------+
```

### 启动 Web 管理界面

PVM 提供了两个 Web 管理界面：

#### 1. PVM 主要的 Web 管理界面

用于 PHP 版本管理、扩展管理、配置管理等。

```bash
# 默认启动 (127.0.0.1:8000)
pvm web

# 自定义主机和端口
pvm web --host=0.0.0.0 --port=8080

# 查看帮助
pvm web --help
```

默认访问地址：http://127.0.0.1:8000

#### 2. PVM 镜像的 Web 界面

用于镜像服务管理、文件下载、状态监控等。

```bash
# 后台启动
php bin/pvm-mirror server start 8080

# 前台启动
php bin/pvm-mirror server start 8080 --foreground

# 停止服务器
php bin/pvm-mirror server stop

# 查看状态
php bin/pvm-mirror server status
```

默认访问地址：http://localhost:8080

## Web 管理界面

Web 管理界面提供了图形化的方式来管理 PHP 版本、扩展和配置。

### 访问 Web 管理界面

启动 Web 管理界面后，使用浏览器访问 http://127.0.0.1:8000（或自定义的主机和端口）。

### 主要功能

Web 管理界面提供以下主要功能：

#### 仪表盘

仪表盘显示系统概览，包括：

- 当前 PHP 版本
- 已安装的 PHP 版本列表
- 已安装的扩展列表
- 系统资源使用情况（CPU、内存、磁盘）

#### 版本管理

版本管理页面允许您：

- 查看已安装的 PHP 版本
- 安装新的 PHP 版本
- 切换当前使用的 PHP 版本
- 删除不再需要的 PHP 版本

#### 扩展管理

扩展管理页面允许您：

- 查看已安装的扩展
- 安装新的扩展
- 启用/禁用扩展
- 配置扩展
- 删除不再需要的扩展

#### Composer 管理

Composer 管理页面允许您：

- 查看当前 Composer 版本
- 安装/更新 Composer
- 管理 Composer 配置
- 执行 Composer 命令

#### 状态监控

状态监控页面提供实时监控功能，包括：

- PHP 进程监控
- PHP-FPM 状态监控
- 系统资源使用监控（CPU、内存、磁盘）
- 错误日志监控

#### 设置

设置页面允许您配置 PVM，包括：

- PHP 配置（php.ini）
- PHP-FPM 配置
- 镜像源配置
- 系统设置

## 状态监控界面

状态监控界面提供了对 PHP 和系统状态的实时监控。

### 主要功能

状态监控界面提供以下主要功能：

#### 实时监控

- PHP 进程监控
- PHP-FPM 进程监控
- 系统资源使用监控（CPU、内存、磁盘）

#### 图表显示

- CPU 使用率图表
- 内存使用率图表
- 请求处理速率图表

#### 日志监控

- PHP 错误日志
- PHP-FPM 错误日志
- PHP-FPM 访问日志
- PHP-FPM 慢日志

#### 报警通知

- 资源使用超阈值报警
- 错误日志报警
- 服务状态变化报警
