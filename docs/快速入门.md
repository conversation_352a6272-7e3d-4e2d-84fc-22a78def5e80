# PVM 快速入门指南

## 🚀 5分钟快速上手

本指南将帮助您在5分钟内快速安装和使用 PHP Version Manager (PVM)。

## 📋 前置要求

- Linux 操作系统（Ubuntu、Debian、CentOS、Fedora、Alpine）
- Bash shell
- 网络连接

## ⚡ 快速安装

### 1. 一键安装
```bash
curl -o- https://raw.githubusercontent.com/dongasai/php-version-manager/main/install.sh | bash
```

### 2. 配置环境
```bash
# 添加到 shell 配置文件
echo 'export PVM_DIR="$HOME/.pvm"' >> ~/.bashrc
echo '[ -s "$PVM_DIR/shell/pvm.sh" ] && \. "$PVM_DIR/shell/pvm.sh"' >> ~/.bashrc

# 重新加载配置
source ~/.bashrc
```

### 3. 验证安装
```bash
pvm --version
```

## 🎯 基本使用

### 查看可用版本
```bash
pvm supported
```

### 安装 PHP 版本
```bash
# 安装最新的 PHP 8.2
pvm install 8.2

# 安装指定版本
pvm install 8.1.27
```

### 切换 PHP 版本
```bash
# 永久切换（全局）
pvm use 8.2

# 临时切换（当前会话）
pvm switch 8.1
```

### 查看已安装版本
```bash
pvm list
```

### 验证当前版本
```bash
php -v
```

## 🔧 常用命令

| 命令 | 功能 |
|------|------|
| `pvm install <version>` | 安装指定PHP版本 |
| `pvm use <version>` | 永久切换PHP版本 |
| `pvm switch <version>` | 临时切换PHP版本 |
| `pvm list` | 查看已安装版本 |
| `pvm supported` | 查看支持的版本 |
| `pvm remove <version>` | 删除指定版本 |
| `pvm status` | 查看PVM状态 |

## 🧩 扩展管理

### 安装扩展
```bash
# 安装 Redis 扩展
pvm ext install redis

# 安装指定版本的扩展
pvm ext install redis --version=5.3.7
```

### 管理扩展
```bash
# 查看已安装扩展
pvm ext list

# 启用扩展
pvm ext enable redis

# 禁用扩展
pvm ext disable redis
```

## 📦 Composer 管理

### 安装 Composer
```bash
pvm composer install
```

### 设置默认版本
```bash
pvm composer default 2.5.8
```

## 🌐 Web 管理界面

### 启动 Web 界面
```bash
pvm web
```

访问 http://127.0.0.1:8000 使用图形界面管理 PHP 版本。

## 🔍 项目级版本管理

### 设置项目专用版本
```bash
# 在项目目录中
cd /path/to/your/project
echo "8.1.27" > .php-version
```

PVM 会自动在该目录中使用指定的 PHP 版本。

## ⚙️ 配置镜像源

### 使用国内镜像（推荐）
```bash
# 启用阿里云镜像
pvm config set mirror.php aliyun
pvm config set mirror.composer aliyun

# 或使用 PVM 自建镜像
pvm pvm-mirror enable
```

## 🆘 常见问题快速解决

### 问题1：找不到 pvm 命令
```bash
# 重新加载 shell 配置
source ~/.bashrc
```

### 问题2：安装失败
```bash
# 初始化环境，安装依赖
pvm init
```

### 问题3：版本切换不生效
```bash
# 检查环境变量
echo $PATH | grep pvm

# 重新初始化
pvm init
```

### 问题4：网络连接问题
```bash
# 使用镜像源
pvm config set mirror.php aliyun
```

## 📚 进一步学习

- **完整文档**: [用户手册](用户手册.md)
- **常见问题**: [常见问题](常见问题.md)
- **版本切换**: [版本切换](版本切换.md)
- **镜像系统**: [镜像](镜像.md)

## 💡 使用技巧

### 1. 批量安装
```bash
# 安装多个版本
for version in 7.4 8.0 8.1 8.2; do
    pvm install $version
done
```

### 2. 自动确认安装
```bash
# 跳过确认提示
pvm install -y 8.2
```

### 3. 静默安装
```bash
# 减少输出信息
pvm install --silent -y 8.2
```

### 4. 详细模式
```bash
# 查看详细安装过程
pvm install --verbose 8.2
```

## 🎉 完成！

恭喜！您已经成功安装并配置了 PVM。现在您可以：

1. ✅ 安装和切换不同的 PHP 版本
2. ✅ 管理 PHP 扩展
3. ✅ 使用 Composer
4. ✅ 通过 Web 界面管理

如果遇到问题，请查看 [常见问题](常见问题.md) 或访问项目 GitHub 仓库获取帮助。

---

**下一步推荐阅读**: [用户手册](用户手册.md) 了解更多高级功能
