# 镜像源下载修复说明

## 问题描述

在之前的版本中，虽然PVM镜像源配置显示"已启用"，但在实际安装PHP时仍然直接从官方源（php.net）下载，没有使用配置的镜像源。

## 问题根因

1. **版本驱动未使用UrlManager**：各个PHP版本驱动（如PHP71/Base.php）直接硬编码官方URL，没有使用UrlManager来获取镜像源URL
2. **DownloadManager不支持URL数组**：DownloadManager的download方法只接受单个URL，无法处理UrlManager返回的URL数组
3. **VersionInstaller也未使用UrlManager**：VersionInstaller中的getSourceUrl方法也是硬编码官方URL

## 修复方案

### 1. 增强DownloadManager支持URL数组

修改 `src/Core/Download/DownloadManager.php`：

- 修改 `download()` 方法支持 `string|array` 类型的URL参数
- 新增 `downloadWithFallback()` 方法，按优先级尝试多个URL
- 增加下载源类型识别和状态显示
- 支持自动回退机制

**关键特性**：
- 优先使用镜像源，失败时自动切换到官方源
- 显示当前使用的下载源类型（镜像源/官方源）
- 提供详细的下载状态信息

### 2. 修改版本驱动使用UrlManager

修改以下文件中的 `getSourceUrl()` 方法：

- `src/Core/VersionInstaller.php`
- `src/Core/Version/GenericVersionDriver.php`
- `src/Core/Version/Drivers/PHP71/Base.php`
- `src/Core/Version/Drivers/PHP54/Base.php`
- `src/Core/Version/Drivers/PHP55/Base.php`
- `src/Core/Version/Drivers/PHP56/Base.php`
- `src/Core/Version/Drivers/PHP80/Base.php`

**修改内容**：
```php
// 修改前
protected function getSourceUrl($version, $mirror = null)
{
    return "https://www.php.net/distributions/php-{$version}.tar.gz";
}

// 修改后
protected function getSourceUrl($version, $mirror = null)
{
    $urlManager = new \VersionManager\Core\Download\UrlManager();
    return $urlManager->getPhpDownloadUrls($version);
}
```

### 3. 更新下载方法调用

修改版本驱动中的下载逻辑：

- PHP71驱动：使用DownloadManager替代curl命令
- 其他驱动：更新变量名和文件名处理

## 修复效果

### 修复前
```
下载PHP 7.1.33 源码...
正在下载 https://www.php.net/distributions/php-7.1.33.tar.gz...
```

### 修复后
```
下载PHP 7.1.33 源码...
尝试从镜像源下载 (第1个源): pvm.2sxo.com
下载文件: php-7.1.33.tar.gz
```

## 验证方法

### 1. 检查镜像源配置
```bash
./bin/pvm pvm-mirror status
```

### 2. 测试URL转换
```php
$urlManager = new UrlManager();
$urls = $urlManager->getPhpDownloadUrls('7.1.33');
// 应该返回镜像源URL在前，官方源URL在后的数组
```

### 3. 实际安装测试
```bash
./bin/pvm install -y 7.1
```

应该看到类似输出：
```
尝试从镜像源下载 (第1个源): pvm.2sxo.com
```

## 技术细节

### URL优先级顺序

1. **主镜像源**：`http://pvm.2sxo.com/php/php-{version}.tar.gz`
2. **备用镜像源**：配置的其他镜像源
3. **官方源**：`https://www.php.net/distributions/php-{version}.tar.gz`

### 自动回退机制

- 如果镜像源下载失败，自动尝试下一个源
- 显示切换信息："正在切换到下一个源..."
- 最终所有源都失败时，抛出详细错误信息

### 错误处理

- 网络连接失败：自动尝试下一个源
- 文件不存在（404）：自动尝试下一个源
- 文件损坏：在验证阶段检测并报告

## 相关文件

- `src/Core/Download/DownloadManager.php` - 核心下载管理
- `src/Core/Download/UrlManager.php` - URL转换管理
- `src/Core/Config/PvmMirrorConfig.php` - 镜像源配置
- `src/Core/VersionInstaller.php` - 版本安装器
- `src/Core/Version/GenericVersionDriver.php` - 通用版本驱动
- `src/Core/Version/Drivers/*/Base.php` - 各版本驱动

## 注意事项

1. **缓存清理**：如果之前有损坏的缓存文件，需要清理缓存
2. **网络连接**：确保能够访问配置的镜像源
3. **镜像源内容**：确保镜像源包含所需的PHP版本文件

## 后续优化建议

1. **镜像源健康检查**：定期检查镜像源可用性
2. **下载速度统计**：记录各源的下载速度，优化排序
3. **断点续传**：支持大文件的断点续传功能
4. **并行下载**：同时从多个源下载，选择最快的
