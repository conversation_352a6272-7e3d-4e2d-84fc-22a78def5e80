# PVM 日志功能

PVM 现在具备完整的日志记录功能，可以记录所有命令的执行过程和详细信息。

## 功能特性

### 1. 自动日志记录
- **命令开始记录**：记录命令名称、参数、开始时间、进程ID、用户和工作目录
- **命令结束记录**：记录结束时间、执行时长、退出代码和执行状态
- **详细过程记录**：记录命令执行过程中的重要步骤和操作

### 2. 日志文件组织
- **存储位置**：
  - **开发模式**：项目根目录的 `logs/` 目录下
  - **生产模式**：`~/.pvm/log/` 目录下
- **文件命名格式**：`年/月/日/时-分-秒.log`
- **示例路径**：
  - 开发模式：`/path/to/pvm/logs/2025/06/04/22-45-30.log`
  - 生产模式：`~/.pvm/log/2025/06/04/22-45-30.log`

### 3. 日志级别
- **INFO**：一般信息记录
- **DEBUG**：调试信息（详细模式下的输出）
- **WARNING**：警告信息
- **ERROR**：错误信息
- **SUCCESS**：成功操作记录

### 4. 日志管理命令

#### 查看日志
```bash
# 显示当前日志文件的最后50行
pvm log show

# 显示最后100行
pvm log show --lines=100

# 显示指定日志文件
pvm log show 2025/06/01/10-30-45.log
```

#### 列出日志文件
```bash
# 列出所有日志文件
pvm log list
```

#### 查看日志路径
```bash
# 显示当前日志文件路径和日志根目录
pvm log path
```

#### 清理过期日志
```bash
# 清理30天前的日志（默认）
pvm log clear

# 清理7天前的日志
pvm log clear --days=7

# 强制清理，不询问确认
pvm log clear --days=7 --force
```

#### 实时查看日志
```bash
# 实时监控当前日志文件
pvm log tail

# 显示最后20行并实时监控
pvm log tail --lines=20
```

## 日志内容示例

### 命令开始记录
```
=== 命令开始 ===
命令: install
参数: -v -y 7.1
开始时间: 2025-06-01 03:04:15
PID: 727321
用户: dongasai
工作目录: /data/wwwroot/php/pvm
```

### 详细过程记录
```
[2025-06-01 03:04:15] [INFO] [INFO] 开始检查PHP版本有效性: 7.1
[2025-06-01 03:04:15] [INFO] [INFO] 尝试自动匹配版本: 7.1
[2025-06-01 03:04:15] [INFO] [INFO] 成功匹配到版本: 7.1.33
[2025-06-01 03:04:15] [INFO] [INFO] 检查版本是否已安装: 7.1.33
[2025-06-01 03:04:15] [INFO] [INFO] 开始检查运行环境
[2025-06-01 03:04:15] [INFO] [INFO] 开始安装PHP版本: 7.1.33
```

### 命令结束记录
```
=== 命令结束 ===
结束时间: 2025-06-01 03:06:54
执行时长: 1秒
退出代码: 0
状态: 成功
```

## 技术实现

### 核心组件

1. **FileLogger 类** (`src/Core/Logger/FileLogger.php`)
   - 负责文件日志的创建、写入和管理
   - 实现按时间格式的目录结构创建
   - 提供不同级别的日志记录方法

2. **Logger 类扩展** (`src/Core/Logger/Logger.php`)
   - 集成文件日志功能到现有的控制台输出系统
   - 同时支持控制台输出和文件记录

3. **LogCommand 类** (`src/Console/Commands/LogCommand.php`)
   - 提供日志管理的命令行接口
   - 支持查看、列表、清理、实时监控等功能

### 集成方式

- **主入口集成**：在 `bin/pvm` 中初始化日志系统
- **命令集成**：在各个命令中添加详细的日志记录
- **异常处理**：捕获并记录未处理的异常

## 开发模式与生产模式

### 开发模式检测
PVM会自动检测是否在开发模式下运行，检测条件包括：

1. **工作目录检查**：当前工作目录是否在项目目录内
2. **项目文件检查**：是否存在 `composer.json`、`bin/pvm`、`src/` 目录
3. **开发环境标识**：是否存在 `docker/`、`tests/` 目录或 `docker-compose.yml` 文件

### 日志存储策略
- **开发模式**：日志存储在项目根目录的 `logs/` 目录下，便于开发调试
- **生产模式**：日志存储在用户主目录的 `~/.pvm/log/` 目录下，避免污染项目目录

## 配置选项

### 启用/禁用文件日志
```php
// 禁用文件日志
\VersionManager\Core\Logger\Logger::setFileLoggingEnabled(false);

// 启用文件日志
\VersionManager\Core\Logger\Logger::setFileLoggingEnabled(true);
```

### 检查日志状态
```php
// 检查文件日志是否启用
$enabled = \VersionManager\Core\Logger\Logger::isFileLoggingEnabled();

// 获取当前日志文件路径
$logFile = \VersionManager\Core\Logger\FileLogger::getCurrentLogFile();
```

## 使用场景

1. **问题诊断**：当安装或操作失败时，查看详细的日志信息
2. **操作审计**：记录所有PVM操作的历史记录
3. **性能分析**：通过执行时长分析命令性能
4. **系统监控**：实时监控PVM的运行状态

## 注意事项

1. **磁盘空间**：日志文件会占用磁盘空间，建议定期清理
2. **权限要求**：确保PVM有权限在 `~/.pvm/log/` 目录下创建文件
3. **性能影响**：文件日志记录对性能影响很小，但在高频操作时可能有轻微影响

## 故障排除

### 日志文件无法创建
- 检查 `~/.pvm/log/` 目录权限
- 确保磁盘空间充足
- 检查文件系统是否支持创建深层目录结构

### 日志内容不完整
- 检查命令是否正常结束
- 查看是否有异常中断
- 确认文件日志功能是否启用

### 日志文件过多
- 使用 `pvm log clear` 命令清理过期日志
- 设置合适的保留天数
- 考虑实现自动清理机制
