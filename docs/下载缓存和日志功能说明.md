# PVM 下载缓存和日志功能说明

## 概述

PVM 现已支持强大的下载缓存和日志记录功能，可以显著提高安装效率并提供详细的操作记录。

## 主要功能

### 1. 下载缓存功能

#### 功能特点
- **智能缓存**: 自动缓存下载的文件，避免重复下载
- **完整性校验**: 支持 MD5、SHA1、SHA256、SHA512 多种校验算法
- **自动清理**: 自动清理过期和损坏的缓存文件
- **缓存验证**: 缓存命中时自动验证文件完整性

#### 缓存策略
- 只缓存完整下载的文件
- 缓存前进行文件有效性检查
- 支持多种校验算法同时验证
- 自动生成和存储文件校验和

#### 缓存目录结构
```
~/.pvm/cache/downloads/
├── [md5_hash]          # 缓存文件
├── [md5_hash].meta     # 元数据文件
└── ...
```

#### 元数据格式
```json
{
    "url": "https://example.com/file.tar.gz",
    "time": 1640995200,
    "size": 12345678,
    "checksums": {
        "md5": "d41d8cd98f00b204e9800998ecf8427e",
        "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"
    },
    "original_path": "/tmp/downloaded_file.tar.gz",
    "cache_version": "1.0"
}
```

### 2. 日志记录功能

#### 日志类型
- **下载日志**: 记录下载开始、完成、失败等事件
- **缓存日志**: 记录缓存命中、未命中、设置、删除等操作
- **完整性日志**: 记录文件校验过程和结果
- **系统日志**: 记录命令执行和系统操作

#### 日志目录结构
```
~/.pvm/log/
├── 2024/
│   ├── 01/
│   │   ├── 15/
│   │   │   ├── 10-30-45.log    # 具体执行日志
│   │   │   └── 14-20-30.log
│   │   └── 16/
│   └── 02/
└── ...
```

#### 日志格式
```
=== 命令开始 ===
命令: install
参数: 8.1.0
开始时间: 2024-01-15 10:30:45
PID: 12345
用户: username
工作目录: /path/to/pvm

[2024-01-15 10:30:46] [INFO] [DOWNLOAD] 开始下载文件
[2024-01-15 10:30:46] [INFO] [DOWNLOAD] URL: https://www.php.net/distributions/php-8.1.0.tar.gz
[2024-01-15 10:30:46] [INFO] [DOWNLOAD] 目标路径: /tmp/php-8.1.0.tar.gz
[2024-01-15 10:30:46] [INFO] [DOWNLOAD] 文件大小: 12.5 MB

[2024-01-15 10:31:15] [INFO] [DOWNLOAD] 下载完成 (来源: 网络)
[2024-01-15 10:31:15] [INFO] [DOWNLOAD] 耗时: 29s
[2024-01-15 10:31:15] [INFO] [DOWNLOAD] 平均速度: 442.3 KB/s

[2024-01-15 10:31:16] [INFO] [INTEGRITY] 文件完整性校验: 通过
[2024-01-15 10:31:16] [INFO] [INTEGRITY] 算法: md5
[2024-01-15 10:31:16] [INFO] [INTEGRITY] 期望值: d41d8cd98f00b204e9800998ecf8427e
[2024-01-15 10:31:16] [INFO] [INTEGRITY] 实际值: d41d8cd98f00b204e9800998ecf8427e

[2024-01-15 10:31:17] [DEBUG] [CACHE] 缓存操作: SET
[2024-01-15 10:31:17] [DEBUG] [CACHE] 键: 5d41402abc4b2a76b9719d911017c592
[2024-01-15 10:31:17] [DEBUG] [CACHE] 详情: 缓存文件: https://www.php.net/distributions/php-8.1.0.tar.gz

=== 命令结束 ===
结束时间: 2024-01-15 10:35:20
执行时长: 4分35秒
退出代码: 0
状态: 成功
```

## 配置选项

### 缓存配置 (`config/download_cache.php`)

```php
return [
    // 基本设置
    'enabled' => true,                      // 启用缓存
    'cache_expire' => 86400 * 7,           // 7天过期
    'max_cache_size' => 1024 * 1024 * 1024, // 1GB最大缓存
    'auto_cleanup' => true,                 // 自动清理
    
    // 完整性校验
    'integrity_check' => [
        'enabled' => true,
        'algorithms' => ['md5', 'sha256'],
        'verify_on_cache_hit' => true,
        'verify_on_cache_set' => true,
    ],
    
    // 日志设置
    'logging' => [
        'enabled' => true,
        'log_cache_hits' => true,
        'log_cache_misses' => true,
        'log_cache_operations' => true,
        'log_integrity_checks' => true,
    ],
];
```

## 使用示例

### 1. 基本下载（自动缓存）
```php
use VersionManager\Core\Download\DownloadManager;

$downloader = new DownloadManager();
$downloader->setUseCache(true);
$downloader->setShowProgress(true);

// 第一次下载（从网络）
$success = $downloader->download(
    'https://www.php.net/distributions/php-8.1.0.tar.gz',
    '/tmp/php-8.1.0.tar.gz'
);

// 第二次下载（从缓存）
$success = $downloader->download(
    'https://www.php.net/distributions/php-8.1.0.tar.gz',
    '/tmp/php-8.1.0-copy.tar.gz'
);
```

### 2. 带校验和的下载
```php
$options = [
    'checksums' => [
        'md5' => 'd41d8cd98f00b204e9800998ecf8427e',
        'sha256' => 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855'
    ]
];

$success = $downloader->download($url, $destination, $options);
```

### 3. 缓存管理
```php
use VersionManager\Core\Cache\CacheManager;

$cache = new CacheManager();

// 获取缓存统计
$stats = $cache->getCacheStats();
echo "缓存文件数: {$stats['download']['count']}\n";
echo "占用空间: " . formatSize($stats['download']['size']) . "\n";

// 清理过期缓存
$cleanupStats = $cache->cleanExpiredDownloadCache();
echo "清理了 {$cleanupStats['expired_removed']} 个过期文件\n";

// 清除所有缓存
$cache->clearDownloadCache();
```

### 4. 完整性校验
```php
use VersionManager\Core\Download\IntegrityVerifier;

// 生成校验和
$checksums = IntegrityVerifier::generateChecksums($filePath, ['md5', 'sha256']);

// 验证文件
$isValid = IntegrityVerifier::verifyFile($filePath, $expectedMd5, 'md5');

// 验证多个校验和
$isValid = IntegrityVerifier::verifyDownloadedFile($filePath, $checksums);
```

## 性能优化

### 缓存命中率优化
- 使用一致的URL格式
- 避免频繁清理缓存
- 合理设置缓存过期时间

### 存储空间优化
- 定期清理过期缓存
- 设置合理的最大缓存大小
- 优先缓存大文件和常用文件

### 网络优化
- 启用缓存减少重复下载
- 使用多线程下载大文件
- 配置合适的超时时间

## 故障排除

### 常见问题

1. **缓存未命中**
   - 检查URL是否完全一致
   - 确认缓存未过期
   - 验证文件完整性

2. **完整性校验失败**
   - 检查校验和是否正确
   - 确认文件未损坏
   - 重新下载文件

3. **日志文件过大**
   - 定期清理旧日志
   - 调整日志级别
   - 使用日志轮转

### 调试方法

1. **启用详细日志**
```php
use VersionManager\Core\Logger\Logger;
Logger::setLevel(LogLevel::DEBUG);
```

2. **检查缓存状态**
```php
$cacheFile = $cache->getDownloadCache($url, false); // 不验证完整性
if ($cacheFile) {
    echo "缓存文件存在: {$cacheFile}\n";
} else {
    echo "缓存文件不存在\n";
}
```

3. **手动验证文件**
```php
$isValid = IntegrityVerifier::isFileValid($filePath);
$checksums = IntegrityVerifier::generateChecksums($filePath);
```

## 最佳实践

1. **启用缓存**: 对于频繁下载的文件，始终启用缓存
2. **定期清理**: 设置自动清理策略，避免缓存占用过多空间
3. **监控日志**: 定期检查日志，及时发现问题
4. **备份配置**: 保存重要的配置文件
5. **测试验证**: 在生产环境使用前充分测试功能
