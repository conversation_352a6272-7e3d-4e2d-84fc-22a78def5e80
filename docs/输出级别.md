# PVM 输出级别文档

## 概述

PVM 支持三个主要的输出级别，用于控制安装过程中的日志详细程度：

- **静默模式 (Silent)**：最少输出，只显示关键信息和错误
- **默认模式 (Normal)**：显示主要操作和结果
- **详细模式 (Verbose)**：显示完整的执行过程和命令输出

## 重要概念区分

### 输出级别 vs 自动确认

**输出级别**和**自动确认**是两个完全独立的功能：

- **输出级别**（`--silent`/`-s`, `--verbose`/`-v`）：控制**显示多少信息**
- **自动确认**（`--yes`/`-y`）：控制**是否跳过交互式确认**

这两个功能可以独立使用，也可以组合使用。

### 自动确认 (-y/--yes)

自动确认参数用于跳过所有交互式提示，自动回答"是"：
- 版本兼容性警告确认
- 环境问题修复确认
- 安装确认等

### 输出级别参数

输出级别参数控制日志的详细程度：
- `--silent`/`-s`：静默模式，最少输出
- 无参数：默认模式，正常输出
- `--verbose`/`-v`：详细模式，完整输出

## 使用方法

```bash
# 静默模式（减少输出）
./bin/pvm install --silent 7.1

# 静默模式 + 自动确认（减少输出且跳过确认）
./bin/pvm install --silent --yes 7.1
./bin/pvm install -s -y 7.1

# 默认模式（正常输出）
./bin/pvm install 7.1

# 默认模式 + 自动确认（正常输出但跳过确认）
./bin/pvm install --yes 7.1
./bin/pvm install -y 7.1

# 详细模式（完整输出）
./bin/pvm install --verbose 7.1

# 详细模式 + 自动确认（完整输出但跳过确认）
./bin/pvm install --verbose --yes 7.1
./bin/pvm install -v -y 7.1
```

## 各级别输出内容对比

### 1. 版本检测和确认阶段

#### 静默模式（不使用 -y）
```
自动匹配到PHP版本 7.1.33
警告: PHP版本 7.1.33 在当前系统上只有部分支持
是否继续安装? (y/n) y
```

#### 静默模式（使用 -y）
```
自动匹配到PHP版本 7.1.33
警告: PHP版本 7.1.33 在当前系统上只有部分支持
自动确认安装
```

#### 默认模式（不使用 -y）
```
自动匹配到PHP版本 7.1.33
警告: PHP版本 7.1.33 在当前系统上只有部分支持
已知问题:
  - 已不再受官方支持，可能存在安全风险
  - 在某些新版本系统上可能无法正常编译
是否继续安装? (y/n) y
```

#### 默认模式（使用 -y）
```
自动匹配到PHP版本 7.1.33
警告: PHP版本 7.1.33 在当前系统上只有部分支持
已知问题:
  - 已不再受官方支持，可能存在安全风险
  - 在某些新版本系统上可能无法正常编译
自动确认安装
```

#### 详细模式（不使用 -y）
```
自动匹配到PHP版本 7.1.33
检测到操作系统: Ubuntu 22.04
选择版本驱动: PHP71\Ubuntu
警告: PHP版本 7.1.33 在当前系统上只有部分支持
已知问题:
  - 已不再受官方支持，可能存在安全风险
  - 在某些新版本系统上可能无法正常编译
  - 某些扩展可能不兼容
是否继续安装? (y/n) y
开始安装流程...
```

#### 详细模式（使用 -y）
```
自动匹配到PHP版本 7.1.33
检测到操作系统: Ubuntu 22.04
选择版本驱动: PHP71\Ubuntu
警告: PHP版本 7.1.33 在当前系统上只有部分支持
已知问题:
  - 已不再受官方支持，可能存在安全风险
  - 在某些新版本系统上可能无法正常编译
  - 某些扩展可能不兼容
自动确认安装
开始安装流程...
```

### 2. 系统依赖安装阶段

#### 静默模式
```
系统依赖安装完成
```

#### 默认模式
```
安装系统依赖...
更新软件包列表...
软件包列表更新成功
安装依赖包: libmcrypt-dev libreadline-dev
依赖包安装成功
系统依赖安装完成
```

#### 详细模式
```
安装系统依赖...
检测到包管理器: apt
更新软件包列表...
执行命令: sudo apt-get update
  命中:1 https://mirrors.cloud.tencent.com/ubuntu jammy InRelease
  命中:2 https://mirrors.cloud.tencent.com/ubuntu jammy-updates InRelease
  命中:3 https://mirrors.cloud.tencent.com/ubuntu jammy-security InRelease
  忽略:4 https://esm.ubuntu.com/apps/ubuntu jammy-apps-security InRelease
  ...
软件包列表更新成功
检查已安装的依赖包...
  build-essential: 已安装
  libxml2-dev: 已安装
  libssl-dev: 已安装
  ...
安装依赖包: libmcrypt-dev libreadline-dev
执行命令: sudo apt-get install -y libmcrypt-dev libreadline-dev
  正在读取软件包列表...
  正在分析软件包的依赖关系树...
  正在读取状态信息...
  ...
依赖包安装成功
系统依赖安装完成
```

### 3. PHP 源码下载阶段

#### 静默模式
```
PHP 7.1.33 安装成功
```

#### 默认模式
```
下载PHP 7.1.33 源码...
源码下载完成
解压源码...
配置编译选项...
编译安装PHP...
PHP 7.1.33 安装成功
```

#### 详细模式
```
下载PHP 7.1.33 源码...
下载URL: https://www.php.net/distributions/php-7.1.33.tar.gz
下载进度: [████████████████████████████████] 100%
源码下载完成 (15.2 MB)
解压源码...
解压到: /tmp/pvm_php_7.1.33_1234567890
配置编译选项...
执行命令: ./configure --prefix=/home/<USER>/.pvm/versions/7.1.33 --enable-fpm --with-mysql --with-pdo-mysql ...
  checking for grep that handles long lines and -e... /usr/bin/grep
  checking for egrep... /usr/bin/grep -E
  checking for a sed that does not truncate output... /usr/bin/sed
  ...
编译安装PHP...
执行命令: make -j4
  /bin/bash /tmp/pvm_php_7.1.33_1234567890/libtool --silent --preserve-dup-deps --mode=compile ...
  ...
执行命令: make install
  Installing PHP SAPI module:       fpm
  Installing PHP CGI binary:        /home/<USER>/.pvm/versions/7.1.33/bin/
  ...
配置PHP...
创建配置文件: /home/<USER>/.pvm/versions/7.1.33/etc/php.ini
创建FPM配置: /home/<USER>/.pvm/versions/7.1.33/etc/php-fpm.conf
PHP 7.1.33 安装成功
```

## 错误和警告处理

### 所有级别都会显示的信息

无论使用哪个级别，以下信息都会显示：

- **错误信息**：安装失败、权限不足、网络错误等
- **警告信息**：版本兼容性警告、网络连接问题等
- **成功信息**：最终安装结果

### 示例：网络问题处理

#### 所有级别
```
警告: 部分软件源连接失败，但主要软件源可用
```

#### 详细模式额外显示
```
网络错误详情:
  错误:8 https://esm.ubuntu.com/apps/ubuntu jammy-apps-security InRelease
    无法连接上 esm.ubuntu.com:443，连接超时
  错误:9 https://esm.ubuntu.com/apps/ubuntu jammy-apps-updates InRelease
    不能连接到 esm.ubuntu.com：https：
```

## 实现细节

### 日志级别常量

```php
class LogLevel
{
    const SILENT = 0;   // 静默模式
    const NORMAL = 1;   // 默认模式
    const VERBOSE = 2;  // 详细模式
    const DEBUG = 3;    // 调试模式（开发用）
}
```

### 日志方法使用指南

```php
// 静默模式及以上显示（关键信息）
Logger::silent("关键操作完成");

// 默认模式及以上显示（一般信息）
Logger::info("正在执行操作...");

// 详细模式及以上显示（详细信息）
Logger::verbose("执行命令: $command");

// 总是显示（错误、警告、成功）
Logger::error("操作失败");
Logger::warning("注意事项");
Logger::success("操作成功");
```

## 各组件输出级别规范

### 1. 操作系统驱动 (OsDriver)

#### updatePackageCache()
- **静默模式**：无输出（除非出错）
- **默认模式**：显示"更新软件包列表..."和结果
- **详细模式**：显示执行的命令和完整输出

#### installPackages()
- **静默模式**：无输出（除非出错）
- **默认模式**：显示"安装依赖包: package1 package2"和结果
- **详细模式**：显示执行的命令、安装过程和完整输出

### 2. 版本驱动 (VersionDriver)

#### installDependencies()
- **静默模式**：无输出（除非出错）
- **默认模式**：显示"安装系统依赖..."和结果
- **详细模式**：显示依赖检查过程和详细安装信息

#### downloadSource()
- **静默模式**：无输出（除非出错）
- **默认模式**：显示"下载PHP源码..."和进度
- **详细模式**：显示下载URL、文件大小、详细进度

#### compileAndInstall()
- **静默模式**：无输出（除非出错）
- **默认模式**：显示"编译安装PHP..."和主要步骤
- **详细模式**：显示configure选项、make输出、安装详情

### 3. 安装器 (VersionInstaller)

#### install()
- **静默模式**：只显示最终结果
- **默认模式**：显示主要安装步骤
- **详细模式**：显示完整安装流程和调试信息

## 性能影响

- **静默模式**：最快，减少I/O操作
- **默认模式**：平衡性能和信息量
- **详细模式**：较慢，但便于调试和监控

## 使用建议

### 静默模式适用场景
- 自动化脚本
- 批量安装
- 生产环境部署
- CI/CD 流水线

### 默认模式适用场景
- 日常使用
- 交互式安装
- 需要了解安装进度

### 详细模式适用场景
- 调试安装问题
- 学习安装过程
- 故障排查
- 开发和测试

## 输出级别迁移指南

### 从旧版本迁移

如果您之前使用的是没有输出级别控制的版本，默认行为相当于新版本的"默认模式"。

### 脚本兼容性

现有脚本无需修改即可继续工作，因为默认模式保持了原有的输出行为。

### 自定义输出级别

开发者可以通过以下方式自定义输出：

```php
// 在代码中设置日志级别
\VersionManager\Core\Logger\Logger::setLevel(\VersionManager\Core\Logger\LogLevel::VERBOSE);

// 检查当前级别
if (\VersionManager\Core\Logger\Logger::isVerbose()) {
    // 执行详细模式特有的操作
}
```

## 参数组合使用场景

### 交互式使用（开发和调试）

```bash
# 正常安装，需要手动确认
./bin/pvm install 7.1

# 详细安装，需要手动确认（用于调试）
./bin/pvm install --verbose 7.1
```

### 自动化脚本使用

```bash
# 静默安装，自动确认（CI/CD 环境）
./bin/pvm install --silent --yes 7.1

# 正常安装，自动确认（自动化脚本）
./bin/pvm install --yes 7.1

# 详细安装，自动确认（调试自动化脚本）
./bin/pvm install --verbose --yes 7.1
```

### 特殊场景

```bash
# 只想减少输出，但保留交互确认
./bin/pvm install --silent 7.1

# 只想跳过确认，但保留完整输出
./bin/pvm install --yes 7.1
```

## 总结

### 核心概念

1. **输出级别**（`--silent`/`-s`, `--verbose`/`-v`）：
   - 控制**显示多少信息**
   - 不影响程序的交互行为
   - 三个级别：静默、默认、详细

2. **自动确认**（`--yes`/`-y`）：
   - 控制**是否跳过交互式确认**
   - 不影响信息输出的详细程度
   - 自动回答所有确认提示为"是"

### 常见误区

❌ **错误理解**：认为 `-y` 是静默模式的一部分
✅ **正确理解**：`-y` 是自动确认，与输出级别无关

❌ **错误用法**：只使用 `-y` 期望减少输出
✅ **正确用法**：使用 `--silent` 减少输出，使用 `-y` 跳过确认

### 最佳实践

- **开发调试**：使用 `--verbose` 获取详细信息
- **日常使用**：使用默认模式（无参数）
- **自动化脚本**：使用 `--silent --yes` 或 `-s -y`
- **CI/CD 环境**：推荐使用 `-s -y` 组合

## 故障排查

### 常见问题

1. **输出过少**：使用 `--verbose` 参数获取更多信息
2. **输出过多**：使用 `--silent` 参数减少输出
3. **脚本解析问题**：确保脚本能正确处理不同级别的输出格式
4. **自动化脚本卡住**：确保使用了 `--yes` 参数跳过交互式确认
5. **混淆概念**：记住 `-y` 是自动确认，不是静默模式

### 调试技巧

```bash
# 保存详细日志到文件（手动确认）
./bin/pvm install --verbose 7.1 2>&1 | tee install.log

# 保存详细日志到文件（自动确认）
./bin/pvm install --verbose --yes 7.1 2>&1 | tee install.log

# 只查看错误信息（静默模式，手动确认）
./bin/pvm install --silent 7.1 2>&1 | grep -E "(错误|警告|失败)"

# 只查看错误信息（静默模式，自动确认）
./bin/pvm install --silent --yes 7.1 2>&1 | grep -E "(错误|警告|失败)"

# 自动化脚本中的典型用法（静默 + 自动确认）
./bin/pvm install -s -y 7.1

# 调试问题时的典型用法（详细 + 自动确认）
./bin/pvm install -v -y 7.1
```
