# PHP版本支持列表

本文档列出了PHP Version Manager (PVM)在不同Linux发行版和处理器架构上支持的PHP版本。

## 支持策略

PVM支持以下PHP版本：
- 所有[官方支持的PHP版本](https://www.php.net/supported-versions.php)
- 部分历史版本（从PHP 7.1开始）

对于每个发行版和架构组合，支持级别分为：
- ✅ **完全支持**：经过测试，所有功能正常工作
- ⚠️ **部分支持**：大部分功能正常工作，但可能存在一些已知问题
- ❌ **不支持**：已知存在严重问题，不建议使用
- 🔄 **待测试**：尚未经过完整测试

## x86_64 (AMD64) 架构

### Ubuntu

| PHP版本 | 22.04 LTS | 20.04 LTS | 18.04 LTS |
|---------|-----------|-----------|-----------|
| 8.3.x   | ✅        | ✅        | ⚠️        |
| 8.2.x   | ✅        | ✅        | ✅        |
| 8.1.x   | ✅        | ✅        | ✅        |
| 8.0.x   | ✅        | ✅        | ✅        |
| 7.4.x   | ✅        | ✅        | ✅        |
| 7.3.x   | ⚠️        | ✅        | ✅        |
| 7.2.x   | ⚠️        | ⚠️        | ✅        |
| 7.1.x   | ⚠️        | ⚠️        | ✅        |

### Debian

| PHP版本 | 12 (Bookworm) | 11 (Bullseye) | 10 (Buster) |
|---------|---------------|---------------|-------------|
| 8.3.x   | ✅            | ⚠️            | ❌          |
| 8.2.x   | ✅            | ✅            | ⚠️          |
| 8.1.x   | ✅            | ✅            | ⚠️          |
| 8.0.x   | ✅            | ✅            | ✅          |
| 7.4.x   | ✅            | ✅            | ✅          |
| 7.3.x   | ⚠️            | ✅            | ✅          |
| 7.2.x   | ⚠️            | ⚠️            | ✅          |
| 7.1.x   | ⚠️            | ⚠️            | ✅          |

### CentOS/RHEL

| PHP版本 | 9 | 8 | 7 |
|---------|---|---|---|
| 8.3.x   | ✅ | ⚠️ | ❌ |
| 8.2.x   | ✅ | ✅ | ⚠️ |
| 8.1.x   | ✅ | ✅ | ⚠️ |
| 8.0.x   | ✅ | ✅ | ✅ |
| 7.4.x   | ✅ | ✅ | ✅ |
| 7.3.x   | ⚠️ | ✅ | ✅ |
| 7.2.x   | ⚠️ | ⚠️ | ✅ |
| 7.1.x   | ⚠️ | ⚠️ | ✅ |

### Fedora

| PHP版本 | 38 | 37 | 36 |
|---------|----|----|----| 
| 8.3.x   | ✅ | ✅ | ⚠️ |
| 8.2.x   | ✅ | ✅ | ✅ |
| 8.1.x   | ✅ | ✅ | ✅ |
| 8.0.x   | ✅ | ✅ | ✅ |
| 7.4.x   | ✅ | ✅ | ✅ |
| 7.3.x   | ⚠️ | ⚠️ | ✅ |
| 7.2.x   | ⚠️ | ⚠️ | ⚠️ |
| 7.1.x   | ⚠️ | ⚠️ | ⚠️ |

### Alpine Linux

| PHP版本 | 3.18 | 3.17 | 3.16 |
|---------|------|------|------|
| 8.3.x   | ✅   | ⚠️   | ❌   |
| 8.2.x   | ✅   | ✅   | ⚠️   |
| 8.1.x   | ✅   | ✅   | ✅   |
| 8.0.x   | ✅   | ✅   | ✅   |
| 7.4.x   | ✅   | ✅   | ✅   |
| 7.3.x   | ⚠️   | ⚠️   | ✅   |
| 7.2.x   | ⚠️   | ⚠️   | ⚠️   |
| 7.1.x   | ❌   | ❌   | ⚠️   |

## ARM64 (AArch64) 架构

### Ubuntu

| PHP版本 | 22.04 LTS | 20.04 LTS | 18.04 LTS |
|---------|-----------|-----------|-----------|
| 8.3.x   | ✅        | ⚠️        | ❌        |
| 8.2.x   | ✅        | ✅        | ⚠️        |
| 8.1.x   | ✅        | ✅        | ✅        |
| 8.0.x   | ✅        | ✅        | ✅        |
| 7.4.x   | ✅        | ✅        | ✅        |
| 7.3.x   | ⚠️        | ✅        | ✅        |
| 7.2.x   | ⚠️        | ⚠️        | ✅        |
| 7.1.x   | ⚠️        | ⚠️        | ⚠️        |

### Debian

| PHP版本 | 12 (Bookworm) | 11 (Bullseye) | 10 (Buster) |
|---------|---------------|---------------|-------------|
| 8.3.x   | ✅            | ⚠️            | ❌          |
| 8.2.x   | ✅            | ✅            | ⚠️          |
| 8.1.x   | ✅            | ✅            | ⚠️          |
| 8.0.x   | ✅            | ✅            | ✅          |
| 7.4.x   | ✅            | ✅            | ✅          |
| 7.3.x   | ⚠️            | ✅            | ✅          |
| 7.2.x   | ⚠️            | ⚠️            | ✅          |
| 7.1.x   | ⚠️            | ⚠️            | ⚠️          |

### Raspberry Pi OS (基于Debian)

| PHP版本 | Bullseye | Buster |
|---------|----------|--------|
| 8.3.x   | ⚠️       | ❌     |
| 8.2.x   | ✅       | ⚠️     |
| 8.1.x   | ✅       | ⚠️     |
| 8.0.x   | ✅       | ✅     |
| 7.4.x   | ✅       | ✅     |
| 7.3.x   | ✅       | ✅     |
| 7.2.x   | ⚠️       | ✅     |
| 7.1.x   | ⚠️       | ⚠️     |

### Alpine Linux

| PHP版本 | 3.18 | 3.17 | 3.16 |
|---------|------|------|------|
| 8.3.x   | ✅   | ⚠️   | ❌   |
| 8.2.x   | ✅   | ✅   | ⚠️   |
| 8.1.x   | ✅   | ✅   | ✅   |
| 8.0.x   | ✅   | ✅   | ✅   |
| 7.4.x   | ✅   | ✅   | ✅   |
| 7.3.x   | ⚠️   | ⚠️   | ✅   |
| 7.2.x   | ⚠️   | ⚠️   | ⚠️   |
| 7.1.x   | ❌   | ❌   | ⚠️   |

## ARMv7 (armhf) 架构

### Raspberry Pi OS (基于Debian)

| PHP版本 | Bullseye | Buster |
|---------|----------|--------|
| 8.3.x   | ❌       | ❌     |
| 8.2.x   | ⚠️       | ❌     |
| 8.1.x   | ✅       | ⚠️     |
| 8.0.x   | ✅       | ⚠️     |
| 7.4.x   | ✅       | ✅     |
| 7.3.x   | ✅       | ✅     |
| 7.2.x   | ✅       | ✅     |
| 7.1.x   | ⚠️       | ✅     |

### Ubuntu

| PHP版本 | 22.04 LTS | 20.04 LTS | 18.04 LTS |
|---------|-----------|-----------|-----------|
| 8.3.x   | ❌        | ❌        | ❌        |
| 8.2.x   | ⚠️        | ⚠️        | ❌        |
| 8.1.x   | ✅        | ⚠️        | ⚠️        |
| 8.0.x   | ✅        | ✅        | ⚠️        |
| 7.4.x   | ✅        | ✅        | ✅        |
| 7.3.x   | ✅        | ✅        | ✅        |
| 7.2.x   | ⚠️        | ✅        | ✅        |
| 7.1.x   | ⚠️        | ⚠️        | ✅        |

## 已知问题和限制

### PHP 8.3.x
- 在较旧的发行版上可能需要更新系统库
- 在ARMv7架构上编译可能会失败

### PHP 8.2.x
- 在某些较旧的发行版上可能需要手动安装依赖

### PHP 8.1.x
- 在ARMv7架构的较旧系统上可能存在性能问题

### PHP 8.0.x
- 在某些Alpine版本上可能需要额外的编译选项

### PHP 7.4.x
- 在最新的发行版上可能需要特定的编译选项

### PHP 7.3.x
- 在最新的发行版上可能存在依赖冲突

### PHP 7.2.x
- 在最新的发行版上可能不受官方支持

### PHP 7.1.x
- 已不再受官方支持，可能存在安全风险
- 在某些新版本系统上可能无法正常编译

## 注意事项

1. **安全考虑**：建议使用仍在官方支持期内的PHP版本，以获取安全更新。

2. **性能考虑**：较新的PHP版本通常在性能上有所改进，特别是在ARM架构上。

3. **依赖关系**：某些PHP版本可能需要特定版本的系统库，PVM会尝试自动安装这些依赖。

4. **编译选项**：在某些环境下，可能需要自定义编译选项以解决特定问题。

5. **预编译包**：对于标记为"完全支持"的组合，PVM提供预编译的二进制包以加速安装过程。

## 更新频率

此支持列表将定期更新，以反映最新的测试结果和兼容性信息。如果您发现任何不准确之处或有新的测试结果要报告，请提交issue或pull request。

最后更新时间：2023年11月
