# PVM 交互操作指南

本文档介绍 PVM (PHP Version Manager) 的交互操作功能，帮助用户更好地使用交互式界面来管理 PHP 版本、扩展和相关服务。

## 概述

PVM 提供了丰富的交互操作功能，包括：

- 交互式主菜单
- 安装向导
- 版本管理菜单
- 扩展管理菜单
- Composer 管理菜单
- 服务管理菜单
- 增强的 Web 界面

## 交互式命令

### 主交互式菜单

启动主交互式菜单：

```bash
pvm interactive
```

主菜单提供以下功能：

- 查看系统状态
- PHP版本管理
- PHP扩展管理
- Composer管理
- 配置管理
- 服务管理
- 缓存管理
- 系统监控
- 更新PVM
- 帮助信息

### 安装向导

使用安装向导进行引导式安装：

```bash
pvm install-wizard
```

安装向导提供四种安装模式：

1. **快速安装**：安装推荐的PHP版本和常用扩展
2. **自定义安装**：选择特定版本和扩展
3. **开发环境**：包含调试工具和开发扩展
4. **生产环境**：优化的配置和必要扩展

### 版本管理菜单

专门的版本管理交互界面：

```bash
pvm version-menu
```

功能包括：
- 查看已安装版本
- 查看支持的版本
- 安装新版本
- 切换版本（永久/临时）
- 删除版本
- 查看当前版本

### 扩展管理菜单

专门的扩展管理交互界面：

```bash
pvm extension-menu
```

功能包括：
- 查看已安装扩展
- 查看可用扩展
- 安装扩展
- 启用/禁用扩展
- 删除扩展
- 查看扩展信息
- 配置扩展

### Composer管理菜单

专门的Composer管理交互界面：

```bash
pvm composer-menu
```

功能包括：
- 查看Composer状态
- 列出已安装的Composer
- 安装Composer
- 切换Composer版本
- 删除Composer
- 更新Composer
- 配置Composer
- 全局包管理

### 服务管理菜单

专门的服务管理交互界面：

```bash
pvm service-menu
```

功能包括：
- PHP-FPM服务管理
- Nginx虚拟主机管理
- Apache虚拟主机管理
- 查看服务状态
- 查看服务日志

## 交互功能特性

### 增强的用户界面

1. **彩色输出**：不同类型的信息使用不同颜色显示
2. **进度条**：长时间操作显示进度条和状态信息
3. **菜单选择**：数字选择菜单，支持默认选项
4. **多选菜单**：支持多项选择，包括全选、全不选、反选
5. **搜索功能**：在选择列表中支持搜索过滤
6. **确认对话框**：危险操作前的确认提示

### 输入验证和反馈

1. **输入验证**：实时验证用户输入的有效性
2. **错误处理**：友好的错误信息和恢复建议
3. **操作反馈**：操作成功/失败的明确反馈
4. **帮助信息**：每个步骤都有相应的帮助信息

### 智能化功能

1. **自动检测**：自动检测系统环境和已安装组件
2. **推荐选项**：基于环境提供推荐的安装选项
3. **依赖管理**：自动处理组件间的依赖关系
4. **状态记忆**：记住用户的选择和偏好

## Web界面增强

### 安装进度监控

Web界面的安装进度页面提供：

1. **实时进度条**：显示安装进度百分比
2. **状态信息**：当前安装步骤和状态
3. **日志输出**：实时显示安装日志
4. **动画效果**：成功/失败的视觉反馈
5. **后续操作**：安装完成后的快捷操作选项

### 交互式操作

1. **一键操作**：常用操作的一键执行
2. **批量操作**：支持批量安装、删除等操作
3. **拖拽排序**：支持拖拽方式调整优先级
4. **实时搜索**：列表页面支持实时搜索过滤

## 使用技巧

### 快速导航

1. 使用数字键快速选择菜单项
2. 使用 `q` 键快速退出当前菜单
3. 使用 `h` 键查看帮助信息
4. 使用 `s` 键进入搜索模式

### 批量操作

1. 在多选菜单中使用 `a` 全选所有项目
2. 使用 `n` 取消所有选择
3. 使用 `i` 反选当前选择
4. 使用逗号分隔多个选项编号

### 搜索和过滤

1. 在选择列表中输入 `s` 进入搜索模式
2. 输入关键词过滤选项
3. 使用 `c` 清除搜索条件
4. 搜索支持部分匹配和大小写不敏感

### 错误恢复

1. 操作失败时会显示详细错误信息
2. 提供重试选项和替代方案
3. 支持回滚到之前的状态
4. 提供故障排除建议

## 配置选项

### 界面配置

可以通过配置文件自定义界面行为：

```bash
# 设置默认菜单样式
pvm config set ui.menu_style numbered

# 设置颜色主题
pvm config set ui.color_theme dark

# 设置进度条样式
pvm config set ui.progress_style detailed
```

### 交互行为

```bash
# 设置确认对话框默认值
pvm config set ui.confirm_default true

# 设置自动完成功能
pvm config set ui.auto_complete true

# 设置搜索敏感度
pvm config set ui.search_case_sensitive false
```

## 故障排除

### 常见问题

1. **菜单显示异常**：检查终端是否支持彩色输出
2. **输入无响应**：确保终端支持交互式输入
3. **进度条不显示**：检查终端宽度是否足够
4. **搜索功能失效**：确认输入法状态

### 调试模式

启用调试模式获取详细信息：

```bash
pvm config set debug.enabled true
pvm config set debug.level verbose
```

### 重置配置

如果遇到配置问题，可以重置到默认状态：

```bash
pvm config reset ui
```

## 最佳实践

1. **首次使用**：建议使用安装向导进行初始设置
2. **日常管理**：使用主交互式菜单进行常规操作
3. **批量操作**：使用专门的管理菜单进行批量操作
4. **Web界面**：复杂操作建议使用Web界面
5. **脚本自动化**：重复操作可以编写脚本自动化

## 更新和维护

交互操作功能会随着PVM版本更新而不断改进。建议：

1. 定期更新PVM到最新版本
2. 关注新功能和改进
3. 提供使用反馈和建议
4. 参与社区讨论和贡献

---

通过这些交互操作功能，PVM 为用户提供了更加友好和高效的PHP版本管理体验。无论是新手还是专业用户，都能找到适合自己的操作方式。
