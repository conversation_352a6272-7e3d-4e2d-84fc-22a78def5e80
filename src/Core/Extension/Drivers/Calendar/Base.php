<?php

namespace VersionManager\Core\Extension\Drivers\Calendar;

use VersionManager\Core\Extension\AbstractExtensionDriver;
use VersionManager\Core\Extension\ExtensionType;

/**
 * Calendar扩展基础驱动类
 */
class Base extends AbstractExtensionDriver
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct(
            'calendar',
            'Calendar Extension',
            '',
            ExtensionType::BUILTIN,
            [],
            [],
            false
        );
    }

    /**
     * {@inheritdoc}
     */
    public function install($phpVersion, array $options = [])
    {
        // 检查扩展是否已安装
        if ($this->isInstalled($phpVersion)) {
            throw new \Exception("扩展 {$this->getName()} 已经安装");
        }

        // 获取PHP二进制文件路径
        $phpBin = $this->getPhpBinary($phpVersion);

        // 检查PHP是否支持该扩展
        $output = [];
        exec($phpBin . ' -m | grep ' . $this->getName(), $output);

        if (empty($output)) {
            // 如果不支持，则安装依赖并重新编译PHP
            $this->installDependencies();
            throw new \Exception("当前 PHP 版本不支持 Calendar 扩展，需要重新编译 PHP");
        }

        // 启用扩展
        $config = isset($options['config']) ? $options['config'] : $this->getDefaultConfig();
        return $this->enable($phpVersion, $config);
    }

    /**
     * 安装扩展依赖
     */
    protected function installDependencies()
    {
        // Calendar通常不需要额外的依赖，它是PHP的内置扩展
    }

    /**
     * 获取操作系统信息
     *
     * @return array [type => 类型, version => 版本]
     */
    protected function getOsInfo()
    {
        $type = '';
        $version = '';

        // 读取/etc/os-release文件
        if (file_exists('/etc/os-release')) {
            $osRelease = parse_ini_file('/etc/os-release');

            if (isset($osRelease['ID'])) {
                $type = strtolower($osRelease['ID']);
            }

            if (isset($osRelease['VERSION_ID'])) {
                $version = $osRelease['VERSION_ID'];
            }
        }

        return [
            'type' => $type,
            'version' => $version,
        ];
    }

    // 使用父类的 remove 方法
}
